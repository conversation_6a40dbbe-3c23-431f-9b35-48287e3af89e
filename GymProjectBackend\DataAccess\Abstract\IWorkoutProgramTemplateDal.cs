using Core.DataAccess;
using Core.Utilities.Paging;
using Entities.Concrete;
using Entities.DTOs;
using System.Collections.Generic;

namespace DataAccess.Abstract
{
    public interface IWorkoutProgramTemplateDal : IEntityRepository<WorkoutProgramTemplate>
    {
        /// <summary>
        /// Tüm antrenman programı şablonlarını getirir (eski metod - geriye uyumluluk için)
        /// </summary>
        List<WorkoutProgramTemplateListDto> GetWorkoutProgramTemplateList();

        /// <summary>
        /// ✅ PERFORMANS OPTİMİZE EDİLMİŞ: Sayfalama ile antrenman programı şablonlarını getirir
        /// </summary>
        PaginatedResult<WorkoutProgramTemplateListDto> GetWorkoutProgramTemplateListPaginated(WorkoutProgramPagingParameters parameters);

        /// <summary>
        /// Belirli bir antrenman programı şablonunun detayını getirir
        /// </summary>
        WorkoutProgramTemplateDto GetWorkoutProgramTemplateDetail(int templateId);
    }
}
