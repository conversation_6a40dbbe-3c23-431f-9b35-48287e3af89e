namespace Core.Utilities.Paging
{
    /// <summary>
    /// Antrenman programı sayfalama parametreleri
    /// </summary>
    public class WorkoutProgramPagingParameters : PagingParameters
    {
        public string? ExperienceLevel { get; set; } // Başlangıç, Orta, İleri
        public string? TargetGoal { get; set; } // Kilo Alma, Kilo Verme, Kas Yapma
        public bool? IsActive { get; set; } = true; // Varsayılan olarak aktif programlar

        public WorkoutProgramPagingParameters()
        {
            PageSize = 20; // Varsayılan sayfa boyutu
            PageNumber = 1; // Varsayılan sayfa numarası
            SearchText = ""; // Boş arama metni
        }
    }
}
