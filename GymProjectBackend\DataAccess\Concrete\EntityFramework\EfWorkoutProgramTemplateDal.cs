using Core.DataAccess.EntityFramework;
using Core.Extensions;
using Core.Utilities.Paging;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System.Collections.Generic;
using System.Linq;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfWorkoutProgramTemplateDal : EfCompanyEntityRepositoryBase<WorkoutProgramTemplate, GymContext>, IWorkoutProgramTemplateDal
    {
        private readonly ICompanyContext _companyContext;

        public EfWorkoutProgramTemplateDal(ICompanyContext companyContext) : base(companyContext)
        {
            _companyContext = companyContext;
        }

        public List<WorkoutProgramTemplateListDto> GetWorkoutProgramTemplateList()
        {
            using (GymContext context = new GymContext())
            {
                int companyId = _companyContext.GetCompanyId();

                // ✅ PERFORMANS OPTİMİZASYONU: Tek sorgu ile tüm bilgileri al
                var result = from wpt in context.WorkoutProgramTemplates
                             where wpt.CompanyID == companyId && wpt.IsActive == true
                             select new
                             {
                                 wpt.WorkoutProgramTemplateID,
                                 wpt.ProgramName,
                                 wpt.Description,
                                 wpt.ExperienceLevel,
                                 wpt.TargetGoal,
                                 wpt.IsActive,
                                 wpt.CreationDate
                             };

                var templates = result.OrderByDescending(x => x.CreationDate).ToList();

                // ✅ Gün ve egzersiz sayılarını tek sorguda hesapla
                var templateIds = templates.Select(t => t.WorkoutProgramTemplateID).ToList();

                var dayCounts = context.WorkoutProgramDays
                    .Where(d => templateIds.Contains(d.WorkoutProgramTemplateID))
                    .GroupBy(d => d.WorkoutProgramTemplateID)
                    .Select(g => new { TemplateId = g.Key, Count = g.Count() })
                    .ToDictionary(x => x.TemplateId, x => x.Count);

                var exerciseCounts = (from wpd in context.WorkoutProgramDays
                                    join wpe in context.WorkoutProgramExercises on wpd.WorkoutProgramDayID equals wpe.WorkoutProgramDayID
                                    where templateIds.Contains(wpd.WorkoutProgramTemplateID)
                                    group wpe by wpd.WorkoutProgramTemplateID into g
                                    select new { TemplateId = g.Key, Count = g.Count() })
                                    .ToDictionary(x => x.TemplateId, x => x.Count);

                // ✅ DTO'ları oluştur
                return templates.Select(t => new WorkoutProgramTemplateListDto
                {
                    WorkoutProgramTemplateID = t.WorkoutProgramTemplateID,
                    ProgramName = t.ProgramName,
                    Description = t.Description,
                    ExperienceLevel = t.ExperienceLevel,
                    TargetGoal = t.TargetGoal,
                    IsActive = t.IsActive,
                    CreationDate = t.CreationDate,
                    DayCount = dayCounts.GetValueOrDefault(t.WorkoutProgramTemplateID, 0),
                    ExerciseCount = exerciseCounts.GetValueOrDefault(t.WorkoutProgramTemplateID, 0)
                }).ToList();
            }
        }

        public WorkoutProgramTemplateDto GetWorkoutProgramTemplateDetail(int templateId)
        {
            using (GymContext context = new GymContext())
            {
                int companyId = _companyContext.GetCompanyId();

                // ✅ PERFORMANS OPTİMİZASYONU: Önce template'i kontrol et
                var template = context.WorkoutProgramTemplates
                    .Where(wpt => wpt.WorkoutProgramTemplateID == templateId && wpt.CompanyID == companyId)
                    .Select(wpt => new WorkoutProgramTemplateDto
                    {
                        WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID,
                        CompanyID = wpt.CompanyID,
                        ProgramName = wpt.ProgramName,
                        Description = wpt.Description,
                        ExperienceLevel = wpt.ExperienceLevel,
                        TargetGoal = wpt.TargetGoal,
                        IsActive = wpt.IsActive,
                        CreationDate = wpt.CreationDate,
                        DayCount = 0 // Aşağıda hesaplanacak
                    })
                    .FirstOrDefault();

                if (template != null)
                {
                    // ✅ Günleri optimize edilmiş şekilde getir
                    template.Days = GetWorkoutProgramDaysOptimized(context, templateId);
                    template.DayCount = template.Days.Count;
                }

                return template;
            }
        }

        /// <summary>
        /// ✅ PERFORMANS OPTİMİZE EDİLMİŞ: Tüm günleri ve egzersizleri tek seferde getirir
        /// </summary>
        private List<WorkoutProgramDayDto> GetWorkoutProgramDaysOptimized(GymContext context, int templateId)
        {
            // ✅ Günleri getir
            var days = context.WorkoutProgramDays
                .Where(d => d.WorkoutProgramTemplateID == templateId)
                .Select(d => new WorkoutProgramDayDto
                {
                    WorkoutProgramDayID = d.WorkoutProgramDayID,
                    WorkoutProgramTemplateID = d.WorkoutProgramTemplateID,
                    DayNumber = d.DayNumber,
                    DayName = d.DayName,
                    IsRestDay = d.IsRestDay,
                    CreationDate = d.CreationDate
                })
                .OrderBy(d => d.DayNumber)
                .ToList();

            if (!days.Any()) return days;

            // ✅ Tüm egzersizleri tek sorguda getir
            var dayIds = days.Select(d => d.WorkoutProgramDayID).ToList();
            var allExercises = GetWorkoutProgramExercisesOptimized(context, dayIds);

            // ✅ Egzersizleri günlere dağıt
            foreach (var day in days)
            {
                day.Exercises = allExercises.Where(e => e.WorkoutProgramDayID == day.WorkoutProgramDayID).ToList();
            }

            return days;
        }

        /// <summary>
        /// ✅ PERFORMANS OPTİMİZE EDİLMİŞ: Birden fazla gün için egzersizleri tek sorguda getirir
        /// </summary>
        private List<WorkoutProgramExerciseDto> GetWorkoutProgramExercisesOptimized(GymContext context, List<int> dayIds)
        {
            // ✅ Tüm egzersizleri tek sorguda getir
            var exercises = (from wpe in context.WorkoutProgramExercises
                           where dayIds.Contains(wpe.WorkoutProgramDayID)
                           select new WorkoutProgramExerciseDto
                           {
                               WorkoutProgramExerciseID = wpe.WorkoutProgramExerciseID,
                               WorkoutProgramDayID = wpe.WorkoutProgramDayID,
                               ExerciseType = wpe.ExerciseType,
                               ExerciseID = wpe.ExerciseID,
                               OrderIndex = wpe.OrderIndex,
                               Sets = wpe.Sets,
                               Reps = wpe.Reps,
                               RestTime = wpe.RestTime,
                               Notes = wpe.Notes,
                               CreationDate = wpe.CreationDate
                           }).OrderBy(e => e.WorkoutProgramDayID).ThenBy(e => e.OrderIndex).ToList();

            if (!exercises.Any()) return exercises;

            // ✅ Sistem egzersizlerini tek sorguda getir
            var systemExerciseIds = exercises.Where(e => e.ExerciseType == "System").Select(e => e.ExerciseID).Distinct().ToList();
            var systemExercises = (from se in context.SystemExercises
                                 join ec in context.ExerciseCategories on se.ExerciseCategoryID equals ec.ExerciseCategoryID
                                 where systemExerciseIds.Contains(se.SystemExerciseID)
                                 select new { se.SystemExerciseID, se.ExerciseName, se.Description, ec.CategoryName })
                                 .ToDictionary(x => x.SystemExerciseID, x => new { x.ExerciseName, x.Description, x.CategoryName });

            // ✅ Şirket egzersizlerini tek sorguda getir
            var companyExerciseIds = exercises.Where(e => e.ExerciseType == "Company").Select(e => e.ExerciseID).Distinct().ToList();
            var companyExercises = (from ce in context.CompanyExercises
                                  join ec in context.ExerciseCategories on ce.ExerciseCategoryID equals ec.ExerciseCategoryID
                                  where companyExerciseIds.Contains(ce.CompanyExerciseID)
                                  select new { ce.CompanyExerciseID, ce.ExerciseName, ce.Description, ec.CategoryName })
                                  .ToDictionary(x => x.CompanyExerciseID, x => new { x.ExerciseName, x.Description, x.CategoryName });

            // ✅ Egzersiz bilgilerini ata
            foreach (var exercise in exercises)
            {
                if (exercise.ExerciseType == "System" && systemExercises.TryGetValue(exercise.ExerciseID, out var systemEx))
                {
                    exercise.ExerciseName = systemEx.ExerciseName;
                    exercise.ExerciseDescription = systemEx.Description;
                    exercise.CategoryName = systemEx.CategoryName;
                }
                else if (exercise.ExerciseType == "Company" && companyExercises.TryGetValue(exercise.ExerciseID, out var companyEx))
                {
                    exercise.ExerciseName = companyEx.ExerciseName;
                    exercise.ExerciseDescription = companyEx.Description;
                    exercise.CategoryName = companyEx.CategoryName;
                }
            }

            return exercises;
        }

        /// <summary>
        /// ✅ PERFORMANS OPTİMİZE EDİLMİŞ: Sayfalama ile antrenman programı şablonlarını getirir
        /// </summary>
        public PaginatedResult<WorkoutProgramTemplateListDto> GetWorkoutProgramTemplateListPaginated(WorkoutProgramPagingParameters parameters)
        {
            using (GymContext context = new GymContext())
            {
                int companyId = _companyContext.GetCompanyId();

                // ✅ Temel sorguyu oluştur
                var query = context.WorkoutProgramTemplates
                    .Where(wpt => wpt.CompanyID == companyId);

                // ✅ Filtreleme
                if (parameters.IsActive.HasValue)
                {
                    query = query.Where(wpt => wpt.IsActive == parameters.IsActive.Value);
                }

                if (!string.IsNullOrWhiteSpace(parameters.SearchText))
                {
                    query = query.Where(wpt =>
                        wpt.ProgramName.Contains(parameters.SearchText) ||
                        (wpt.Description != null && wpt.Description.Contains(parameters.SearchText)));
                }

                if (!string.IsNullOrWhiteSpace(parameters.ExperienceLevel))
                {
                    query = query.Where(wpt => wpt.ExperienceLevel == parameters.ExperienceLevel);
                }

                if (!string.IsNullOrWhiteSpace(parameters.TargetGoal))
                {
                    query = query.Where(wpt => wpt.TargetGoal == parameters.TargetGoal);
                }

                // ✅ Sıralama
                query = query.OrderByDescending(wpt => wpt.CreationDate);

                // ✅ Sayfalama için toplam sayıyı al
                var totalCount = query.Count();

                // ✅ Sayfalama uygula ve temel bilgileri al
                var templates = query
                    .Skip((parameters.PageNumber - 1) * parameters.PageSize)
                    .Take(parameters.PageSize)
                    .Select(wpt => new
                    {
                        wpt.WorkoutProgramTemplateID,
                        wpt.ProgramName,
                        wpt.Description,
                        wpt.ExperienceLevel,
                        wpt.TargetGoal,
                        wpt.IsActive,
                        wpt.CreationDate
                    })
                    .ToList();

                if (!templates.Any())
                {
                    return new PaginatedResult<WorkoutProgramTemplateListDto>(
                        new List<WorkoutProgramTemplateListDto>(),
                        parameters.PageNumber,
                        parameters.PageSize,
                        totalCount);
                }

                // ✅ Gün ve egzersiz sayılarını tek sorguda hesapla
                var templateIds = templates.Select(t => t.WorkoutProgramTemplateID).ToList();

                var dayCounts = context.WorkoutProgramDays
                    .Where(d => templateIds.Contains(d.WorkoutProgramTemplateID))
                    .GroupBy(d => d.WorkoutProgramTemplateID)
                    .Select(g => new { TemplateId = g.Key, Count = g.Count() })
                    .ToDictionary(x => x.TemplateId, x => x.Count);

                var exerciseCounts = (from wpd in context.WorkoutProgramDays
                                    join wpe in context.WorkoutProgramExercises on wpd.WorkoutProgramDayID equals wpe.WorkoutProgramDayID
                                    where templateIds.Contains(wpd.WorkoutProgramTemplateID)
                                    group wpe by wpd.WorkoutProgramTemplateID into g
                                    select new { TemplateId = g.Key, Count = g.Count() })
                                    .ToDictionary(x => x.TemplateId, x => x.Count);

                // ✅ DTO'ları oluştur
                var result = templates.Select(t => new WorkoutProgramTemplateListDto
                {
                    WorkoutProgramTemplateID = t.WorkoutProgramTemplateID,
                    ProgramName = t.ProgramName,
                    Description = t.Description,
                    ExperienceLevel = t.ExperienceLevel,
                    TargetGoal = t.TargetGoal,
                    IsActive = t.IsActive,
                    CreationDate = t.CreationDate,
                    DayCount = dayCounts.GetValueOrDefault(t.WorkoutProgramTemplateID, 0),
                    ExerciseCount = exerciseCounts.GetValueOrDefault(t.WorkoutProgramTemplateID, 0)
                }).ToList();

                return new PaginatedResult<WorkoutProgramTemplateListDto>(result, parameters.PageNumber, parameters.PageSize, totalCount);
            }
        }
    }
}
