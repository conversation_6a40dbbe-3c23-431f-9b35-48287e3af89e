namespace Core.Utilities.Paging
{
    /// <summary>
    /// <PERSON>ye antrenman programı atama sayfalama parametreleri
    /// </summary>
    public class MemberWorkoutProgramPagingParameters : PagingParameters
    {
        public int? MemberID { get; set; } // Belirli üye için filtreleme
        public int? WorkoutProgramTemplateID { get; set; } // Belirli program için filtreleme
        public string? ExperienceLevel { get; set; } // Program seviyesi
        public string? TargetGoal { get; set; } // Program hedefi
        public bool? IsActive { get; set; } = true; // Varsayılan olarak aktif atamalar
        public DateTime? StartDateFrom { get; set; } // Başlangıç tarihi aralığı
        public DateTime? StartDateTo { get; set; } // Başlangıç tarihi aralığı

        public MemberWorkoutProgramPagingParameters()
        {
            PageSize = 25; // Varsayılan sayfa boyutu
            PageNumber = 1; // Varsayılan sayfa numarası
            SearchText = ""; // Boş arama metni
        }
    }
}
