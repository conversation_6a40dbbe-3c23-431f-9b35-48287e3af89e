using Core.DataAccess.EntityFramework;
using Core.Extensions;
using Core.Utilities.Paging;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using Entities.DTOs;
using System.Collections.Generic;
using System.Linq;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfMemberWorkoutProgramDal : EfEntityRepositoryBase<MemberWorkoutProgram, GymContext>, IMemberWorkoutProgramDal
    {
        private readonly ICompanyContext _companyContext;

        public EfMemberWorkoutProgramDal(ICompanyContext companyContext)
        {
            _companyContext = companyContext;
        }
        public List<MemberWorkoutProgramListDto> GetCompanyAssignments(int companyId)
        {
            using (GymContext context = new GymContext())
            {
                // ✅ PERFORMANS OPTİMİZASYONU: Önce temel bilgileri al
                var assignments = (from mwp in context.MemberWorkoutPrograms
                                 join m in context.Members on mwp.MemberID equals m.MemberID
                                 join wpt in context.WorkoutProgramTemplates on mwp.WorkoutProgramTemplateID equals wpt.WorkoutProgramTemplateID
                                 where mwp.CompanyID == companyId && mwp.IsActive == true
                                 orderby mwp.CreationDate descending
                                 select new
                                 {
                                     mwp.MemberWorkoutProgramID,
                                     mwp.MemberID,
                                     MemberName = m.Name,
                                     ProgramName = wpt.ProgramName,
                                     wpt.ExperienceLevel,
                                     wpt.TargetGoal,
                                     mwp.StartDate,
                                     mwp.EndDate,
                                     mwp.IsActive,
                                     TemplateId = wpt.WorkoutProgramTemplateID
                                 }).ToList();

                if (!assignments.Any()) return new List<MemberWorkoutProgramListDto>();

                // ✅ Gün ve egzersiz sayılarını tek sorguda hesapla
                var templateIds = assignments.Select(a => a.TemplateId).Distinct().ToList();

                var dayCounts = context.WorkoutProgramDays
                    .Where(d => templateIds.Contains(d.WorkoutProgramTemplateID))
                    .GroupBy(d => d.WorkoutProgramTemplateID)
                    .Select(g => new { TemplateId = g.Key, Count = g.Count() })
                    .ToDictionary(x => x.TemplateId, x => x.Count);

                var exerciseCounts = (from wpd in context.WorkoutProgramDays
                                    join wpe in context.WorkoutProgramExercises on wpd.WorkoutProgramDayID equals wpe.WorkoutProgramDayID
                                    where templateIds.Contains(wpd.WorkoutProgramTemplateID)
                                    group wpe by wpd.WorkoutProgramTemplateID into g
                                    select new { TemplateId = g.Key, Count = g.Count() })
                                    .ToDictionary(x => x.TemplateId, x => x.Count);

                // ✅ DTO'ları oluştur
                return assignments.Select(a => new MemberWorkoutProgramListDto
                {
                    MemberWorkoutProgramID = a.MemberWorkoutProgramID,
                    MemberID = a.MemberID,
                    MemberName = a.MemberName,
                    ProgramName = a.ProgramName,
                    ExperienceLevel = a.ExperienceLevel,
                    TargetGoal = a.TargetGoal,
                    StartDate = a.StartDate,
                    EndDate = a.EndDate,
                    IsActive = a.IsActive,
                    DayCount = dayCounts.GetValueOrDefault(a.TemplateId, 0),
                    ExerciseCount = exerciseCounts.GetValueOrDefault(a.TemplateId, 0)
                }).ToList();
            }
        }

        public List<MemberWorkoutProgramDto> GetMemberActivePrograms(int memberId)
        {
            using (GymContext context = new GymContext())
            {
                // ✅ PERFORMANS OPTİMİZASYONU: Önce temel bilgileri al
                var programs = (from mwp in context.MemberWorkoutPrograms
                              join m in context.Members on mwp.MemberID equals m.MemberID
                              join wpt in context.WorkoutProgramTemplates on mwp.WorkoutProgramTemplateID equals wpt.WorkoutProgramTemplateID
                              where mwp.MemberID == memberId && mwp.IsActive == true
                              orderby mwp.StartDate descending
                              select new
                              {
                                  mwp.MemberWorkoutProgramID,
                                  mwp.MemberID,
                                  MemberName = m.Name,
                                  MemberPhone = m.PhoneNumber,
                                  mwp.WorkoutProgramTemplateID,
                                  ProgramName = wpt.ProgramName,
                                  ProgramDescription = wpt.Description,
                                  wpt.ExperienceLevel,
                                  wpt.TargetGoal,
                                  mwp.CompanyID,
                                  mwp.StartDate,
                                  mwp.EndDate,
                                  mwp.Notes,
                                  mwp.IsActive,
                                  mwp.CreationDate
                              }).ToList();

                if (!programs.Any()) return new List<MemberWorkoutProgramDto>();

                // ✅ Gün ve egzersiz sayılarını tek sorguda hesapla
                var templateIds = programs.Select(p => p.WorkoutProgramTemplateID).Distinct().ToList();

                var dayCounts = context.WorkoutProgramDays
                    .Where(d => templateIds.Contains(d.WorkoutProgramTemplateID))
                    .GroupBy(d => d.WorkoutProgramTemplateID)
                    .Select(g => new { TemplateId = g.Key, Count = g.Count() })
                    .ToDictionary(x => x.TemplateId, x => x.Count);

                var exerciseCounts = (from wpd in context.WorkoutProgramDays
                                    join wpe in context.WorkoutProgramExercises on wpd.WorkoutProgramDayID equals wpe.WorkoutProgramDayID
                                    where templateIds.Contains(wpd.WorkoutProgramTemplateID)
                                    group wpe by wpd.WorkoutProgramTemplateID into g
                                    select new { TemplateId = g.Key, Count = g.Count() })
                                    .ToDictionary(x => x.TemplateId, x => x.Count);

                // ✅ DTO'ları oluştur
                return programs.Select(p => new MemberWorkoutProgramDto
                {
                    MemberWorkoutProgramID = p.MemberWorkoutProgramID,
                    MemberID = p.MemberID,
                    MemberName = p.MemberName,
                    MemberPhone = p.MemberPhone,
                    WorkoutProgramTemplateID = p.WorkoutProgramTemplateID,
                    ProgramName = p.ProgramName,
                    ProgramDescription = p.ProgramDescription,
                    ExperienceLevel = p.ExperienceLevel,
                    TargetGoal = p.TargetGoal,
                    CompanyID = p.CompanyID,
                    StartDate = p.StartDate,
                    EndDate = p.EndDate,
                    Notes = p.Notes,
                    IsActive = p.IsActive,
                    CreationDate = p.CreationDate,
                    DayCount = dayCounts.GetValueOrDefault(p.WorkoutProgramTemplateID, 0),
                    ExerciseCount = exerciseCounts.GetValueOrDefault(p.WorkoutProgramTemplateID, 0)
                }).ToList();
            }
        }

        public List<MemberWorkoutProgramHistoryDto> GetMemberProgramHistory(int memberId)
        {
            using (GymContext context = new GymContext())
            {
                var result = from mwp in context.MemberWorkoutPrograms
                             join wpt in context.WorkoutProgramTemplates on mwp.WorkoutProgramTemplateID equals wpt.WorkoutProgramTemplateID
                             where mwp.MemberID == memberId
                             orderby mwp.CreationDate descending
                             select new MemberWorkoutProgramHistoryDto
                             {
                                 MemberWorkoutProgramID = mwp.MemberWorkoutProgramID,
                                 ProgramName = wpt.ProgramName,
                                 StartDate = mwp.StartDate,
                                 EndDate = mwp.EndDate,
                                 IsActive = mwp.IsActive,
                                 Notes = mwp.Notes,
                                 CreationDate = mwp.CreationDate
                             };

                return result.ToList();
            }
        }

        public List<MemberActiveWorkoutProgramDto> GetActiveWorkoutProgramsByUserId(int userId)
        {
            using (GymContext context = new GymContext())
            {
                // ✅ PERFORMANS OPTİMİZASYONU: Önce temel bilgileri al
                var programs = (from mwp in context.MemberWorkoutPrograms
                              join m in context.Members on mwp.MemberID equals m.MemberID
                              join wpt in context.WorkoutProgramTemplates on mwp.WorkoutProgramTemplateID equals wpt.WorkoutProgramTemplateID
                              where m.UserID == userId && mwp.IsActive == true && m.IsActive == true
                                    && mwp.CompanyID == m.CompanyID && wpt.CompanyID == m.CompanyID
                              orderby mwp.StartDate descending
                              select new
                              {
                                  mwp.MemberWorkoutProgramID,
                                  mwp.WorkoutProgramTemplateID,
                                  ProgramName = wpt.ProgramName,
                                  ProgramDescription = wpt.Description,
                                  wpt.ExperienceLevel,
                                  wpt.TargetGoal,
                                  mwp.StartDate,
                                  mwp.EndDate,
                                  mwp.Notes
                              }).ToList();

                if (!programs.Any()) return new List<MemberActiveWorkoutProgramDto>();

                // ✅ Gün ve egzersiz sayılarını tek sorguda hesapla
                var templateIds = programs.Select(p => p.WorkoutProgramTemplateID).Distinct().ToList();

                var dayCounts = context.WorkoutProgramDays
                    .Where(d => templateIds.Contains(d.WorkoutProgramTemplateID))
                    .GroupBy(d => d.WorkoutProgramTemplateID)
                    .Select(g => new { TemplateId = g.Key, Count = g.Count() })
                    .ToDictionary(x => x.TemplateId, x => x.Count);

                var exerciseCounts = (from wpd in context.WorkoutProgramDays
                                    join wpe in context.WorkoutProgramExercises on wpd.WorkoutProgramDayID equals wpe.WorkoutProgramDayID
                                    where templateIds.Contains(wpd.WorkoutProgramTemplateID)
                                    group wpe by wpd.WorkoutProgramTemplateID into g
                                    select new { TemplateId = g.Key, Count = g.Count() })
                                    .ToDictionary(x => x.TemplateId, x => x.Count);

                // ✅ DTO'ları oluştur
                return programs.Select(p => new MemberActiveWorkoutProgramDto
                {
                    MemberWorkoutProgramID = p.MemberWorkoutProgramID,
                    WorkoutProgramTemplateID = p.WorkoutProgramTemplateID,
                    ProgramName = p.ProgramName,
                    ProgramDescription = p.ProgramDescription,
                    ExperienceLevel = p.ExperienceLevel,
                    TargetGoal = p.TargetGoal,
                    StartDate = p.StartDate,
                    EndDate = p.EndDate,
                    Notes = p.Notes,
                    DayCount = dayCounts.GetValueOrDefault(p.WorkoutProgramTemplateID, 0),
                    ExerciseCount = exerciseCounts.GetValueOrDefault(p.WorkoutProgramTemplateID, 0)
                }).ToList();
            }
        }

        public MemberWorkoutProgramDto GetAssignmentDetail(int assignmentId)
        {
            using (GymContext context = new GymContext())
            {
                var result = from mwp in context.MemberWorkoutPrograms
                             join m in context.Members on mwp.MemberID equals m.MemberID
                             join wpt in context.WorkoutProgramTemplates on mwp.WorkoutProgramTemplateID equals wpt.WorkoutProgramTemplateID
                             where mwp.MemberWorkoutProgramID == assignmentId
                             select new MemberWorkoutProgramDto
                             {
                                 MemberWorkoutProgramID = mwp.MemberWorkoutProgramID,
                                 MemberID = mwp.MemberID,
                                 MemberName = m.Name,
                                 MemberPhone = m.PhoneNumber,
                                 WorkoutProgramTemplateID = mwp.WorkoutProgramTemplateID,
                                 ProgramName = wpt.ProgramName,
                                 ProgramDescription = wpt.Description,
                                 ExperienceLevel = wpt.ExperienceLevel,
                                 TargetGoal = wpt.TargetGoal,
                                 CompanyID = mwp.CompanyID,
                                 StartDate = mwp.StartDate,
                                 EndDate = mwp.EndDate,
                                 Notes = mwp.Notes,
                                 IsActive = mwp.IsActive,
                                 CreationDate = mwp.CreationDate
                             };

                return result.FirstOrDefault();
            }
        }

        public int GetAssignedMemberCount(int workoutProgramTemplateId)
        {
            using (GymContext context = new GymContext())
            {
                return context.MemberWorkoutPrograms
                    .Where(mwp => mwp.WorkoutProgramTemplateID == workoutProgramTemplateId && mwp.IsActive == true)
                    .Count();
            }
        }

        public int GetActiveAssignmentCount(int companyId)
        {
            using (GymContext context = new GymContext())
            {
                return context.MemberWorkoutPrograms
                    .Where(mwp => mwp.CompanyID == companyId && mwp.IsActive == true)
                    .Count();
            }
        }

        public MemberWorkoutProgramDetailDto GetProgramDetailByUser(int userId, int memberWorkoutProgramId)
        {
            using (GymContext context = new GymContext())
            {
                // Önce üyenin bu programa erişim yetkisi var mı kontrol et
                var assignment = (from mwp in context.MemberWorkoutPrograms
                                 join m in context.Members on mwp.MemberID equals m.MemberID
                                 join wpt in context.WorkoutProgramTemplates on mwp.WorkoutProgramTemplateID equals wpt.WorkoutProgramTemplateID
                                 where m.UserID == userId && mwp.MemberWorkoutProgramID == memberWorkoutProgramId && mwp.IsActive == true && m.IsActive == true
                                       && mwp.CompanyID == m.CompanyID && wpt.CompanyID == m.CompanyID
                                 select new MemberWorkoutProgramDetailDto
                                 {
                                     MemberWorkoutProgramID = mwp.MemberWorkoutProgramID,
                                     MemberID = mwp.MemberID,
                                     MemberName = m.Name,
                                     WorkoutProgramTemplateID = mwp.WorkoutProgramTemplateID,
                                     ProgramName = wpt.ProgramName,
                                     ProgramDescription = wpt.Description,
                                     ExperienceLevel = wpt.ExperienceLevel,
                                     TargetGoal = wpt.TargetGoal,
                                     StartDate = mwp.StartDate,
                                     EndDate = mwp.EndDate,
                                     Notes = mwp.Notes,
                                     IsActive = mwp.IsActive,
                                     // ✅ Sayılar aşağıda hesaplanacak
                                     DayCount = 0,
                                     ExerciseCount = 0
                                 }).FirstOrDefault();

                if (assignment != null)
                {
                    // ✅ Gün ve egzersiz sayılarını hesapla
                    var dayCount = context.WorkoutProgramDays
                        .Where(wpd => wpd.WorkoutProgramTemplateID == assignment.WorkoutProgramTemplateID)
                        .Count();

                    var exerciseCount = (from wpd in context.WorkoutProgramDays
                                       join wpe in context.WorkoutProgramExercises on wpd.WorkoutProgramDayID equals wpe.WorkoutProgramDayID
                                       where wpd.WorkoutProgramTemplateID == assignment.WorkoutProgramTemplateID
                                       select wpe.WorkoutProgramExerciseID).Count();

                    assignment.DayCount = dayCount;
                    assignment.ExerciseCount = exerciseCount;

                    // ✅ Program günlerini ve egzersizlerini optimize edilmiş şekilde getir
                    assignment.Days = GetWorkoutProgramDaysWithExercisesOptimized(context, assignment.WorkoutProgramTemplateID);
                }

                return assignment;
            }
        }

        /// <summary>
        /// ✅ PERFORMANS OPTİMİZE EDİLMİŞ: Program günlerini egzersizleriyle birlikte getirir
        /// </summary>
        private List<WorkoutProgramDayDto> GetWorkoutProgramDaysWithExercisesOptimized(GymContext context, int workoutProgramTemplateId)
        {
            var days = (from wpd in context.WorkoutProgramDays
                       where wpd.WorkoutProgramTemplateID == workoutProgramTemplateId
                       orderby wpd.DayNumber
                       select new WorkoutProgramDayDto
                       {
                           WorkoutProgramDayID = wpd.WorkoutProgramDayID,
                           WorkoutProgramTemplateID = wpd.WorkoutProgramTemplateID,
                           DayNumber = wpd.DayNumber,
                           DayName = wpd.DayName,
                           IsRestDay = wpd.IsRestDay,
                           CreationDate = wpd.CreationDate
                       }).ToList();

            if (!days.Any()) return days;

            // ✅ Tüm egzersizleri tek sorguda getir
            var dayIds = days.Select(d => d.WorkoutProgramDayID).ToList();
            var allExercises = GetWorkoutProgramExercisesOptimized(context, dayIds);

            // ✅ Egzersizleri günlere dağıt
            foreach (var day in days)
            {
                day.Exercises = allExercises.Where(e => e.WorkoutProgramDayID == day.WorkoutProgramDayID).ToList();
            }

            return days;
        }

        /// <summary>
        /// ✅ PERFORMANS OPTİMİZE EDİLMİŞ: Birden fazla gün için egzersizleri tek sorguda getirir
        /// </summary>
        private List<WorkoutProgramExerciseDto> GetWorkoutProgramExercisesOptimized(GymContext context, List<int> dayIds)
        {
            // ✅ Tüm egzersizleri tek sorguda getir
            var exercises = (from wpe in context.WorkoutProgramExercises
                           where dayIds.Contains(wpe.WorkoutProgramDayID)
                           select new WorkoutProgramExerciseDto
                           {
                               WorkoutProgramExerciseID = wpe.WorkoutProgramExerciseID,
                               WorkoutProgramDayID = wpe.WorkoutProgramDayID,
                               ExerciseType = wpe.ExerciseType,
                               ExerciseID = wpe.ExerciseID,
                               OrderIndex = wpe.OrderIndex,
                               Sets = wpe.Sets,
                               Reps = wpe.Reps,
                               RestTime = wpe.RestTime,
                               Notes = wpe.Notes,
                               CreationDate = wpe.CreationDate
                           }).OrderBy(e => e.WorkoutProgramDayID).ThenBy(e => e.OrderIndex).ToList();

            if (!exercises.Any()) return exercises;

            // ✅ Sistem egzersizlerini tek sorguda getir
            var systemExerciseIds = exercises.Where(e => e.ExerciseType == "System").Select(e => e.ExerciseID).Distinct().ToList();
            var systemExercises = (from se in context.SystemExercises
                                 join ec in context.ExerciseCategories on se.ExerciseCategoryID equals ec.ExerciseCategoryID
                                 where systemExerciseIds.Contains(se.SystemExerciseID)
                                 select new { se.SystemExerciseID, se.ExerciseName, se.Description, ec.CategoryName })
                                 .ToDictionary(x => x.SystemExerciseID, x => new { x.ExerciseName, x.Description, x.CategoryName });

            // ✅ Şirket egzersizlerini tek sorguda getir
            var companyExerciseIds = exercises.Where(e => e.ExerciseType == "Company").Select(e => e.ExerciseID).Distinct().ToList();
            var companyExercises = (from ce in context.CompanyExercises
                                  join ec in context.ExerciseCategories on ce.ExerciseCategoryID equals ec.ExerciseCategoryID
                                  where companyExerciseIds.Contains(ce.CompanyExerciseID)
                                  select new { ce.CompanyExerciseID, ce.ExerciseName, ce.Description, ec.CategoryName })
                                  .ToDictionary(x => x.CompanyExerciseID, x => new { x.ExerciseName, x.Description, x.CategoryName });

            // ✅ Egzersiz bilgilerini ata
            foreach (var exercise in exercises)
            {
                if (exercise.ExerciseType == "System" && systemExercises.TryGetValue(exercise.ExerciseID, out var systemEx))
                {
                    exercise.ExerciseName = systemEx.ExerciseName;
                    exercise.ExerciseDescription = systemEx.Description;
                    exercise.CategoryName = systemEx.CategoryName;
                }
                else if (exercise.ExerciseType == "Company" && companyExercises.TryGetValue(exercise.ExerciseID, out var companyEx))
                {
                    exercise.ExerciseName = companyEx.ExerciseName;
                    exercise.ExerciseDescription = companyEx.Description;
                    exercise.CategoryName = companyEx.CategoryName;
                }
            }

            return exercises;
        }

        /// <summary>
        /// ✅ PERFORMANS OPTİMİZE EDİLMİŞ: Sayfalama ile şirket bazlı program atamalarını getirir
        /// </summary>
        public PaginatedResult<MemberWorkoutProgramListDto> GetCompanyAssignmentsPaginated(MemberWorkoutProgramPagingParameters parameters)
        {
            using (GymContext context = new GymContext())
            {
                int companyId = _companyContext.GetCompanyId();

                // ✅ Temel sorguyu oluştur
                var query = from mwp in context.MemberWorkoutPrograms
                           join m in context.Members on mwp.MemberID equals m.MemberID
                           join wpt in context.WorkoutProgramTemplates on mwp.WorkoutProgramTemplateID equals wpt.WorkoutProgramTemplateID
                           where mwp.CompanyID == companyId
                           select new { mwp, m, wpt };

                // ✅ Filtreleme
                if (parameters.IsActive.HasValue)
                {
                    query = query.Where(x => x.mwp.IsActive == parameters.IsActive.Value);
                }

                if (parameters.MemberID.HasValue)
                {
                    query = query.Where(x => x.mwp.MemberID == parameters.MemberID.Value);
                }

                if (parameters.WorkoutProgramTemplateID.HasValue)
                {
                    query = query.Where(x => x.mwp.WorkoutProgramTemplateID == parameters.WorkoutProgramTemplateID.Value);
                }

                if (!string.IsNullOrWhiteSpace(parameters.SearchText))
                {
                    query = query.Where(x =>
                        x.m.Name.Contains(parameters.SearchText) ||
                        x.wpt.ProgramName.Contains(parameters.SearchText) ||
                        x.m.PhoneNumber.Contains(parameters.SearchText));
                }

                if (!string.IsNullOrWhiteSpace(parameters.ExperienceLevel))
                {
                    query = query.Where(x => x.wpt.ExperienceLevel == parameters.ExperienceLevel);
                }

                if (!string.IsNullOrWhiteSpace(parameters.TargetGoal))
                {
                    query = query.Where(x => x.wpt.TargetGoal == parameters.TargetGoal);
                }

                if (parameters.StartDateFrom.HasValue)
                {
                    query = query.Where(x => x.mwp.StartDate >= parameters.StartDateFrom.Value);
                }

                if (parameters.StartDateTo.HasValue)
                {
                    query = query.Where(x => x.mwp.StartDate <= parameters.StartDateTo.Value);
                }

                // ✅ Sıralama
                query = query.OrderByDescending(x => x.mwp.CreationDate);

                // ✅ Sayfalama için toplam sayıyı al
                var totalCount = query.Count();

                // ✅ Sayfalama uygula ve temel bilgileri al
                var assignments = query
                    .Skip((parameters.PageNumber - 1) * parameters.PageSize)
                    .Take(parameters.PageSize)
                    .Select(x => new
                    {
                        x.mwp.MemberWorkoutProgramID,
                        x.mwp.MemberID,
                        MemberName = x.m.Name,
                        ProgramName = x.wpt.ProgramName,
                        x.wpt.ExperienceLevel,
                        x.wpt.TargetGoal,
                        x.mwp.StartDate,
                        x.mwp.EndDate,
                        x.mwp.IsActive,
                        TemplateId = x.wpt.WorkoutProgramTemplateID
                    })
                    .ToList();

                if (!assignments.Any())
                {
                    return new PaginatedResult<MemberWorkoutProgramListDto>(
                        new List<MemberWorkoutProgramListDto>(),
                        parameters.PageNumber,
                        parameters.PageSize,
                        totalCount);
                }

                // ✅ Gün ve egzersiz sayılarını tek sorguda hesapla
                var templateIds = assignments.Select(a => a.TemplateId).Distinct().ToList();

                var dayCounts = context.WorkoutProgramDays
                    .Where(d => templateIds.Contains(d.WorkoutProgramTemplateID))
                    .GroupBy(d => d.WorkoutProgramTemplateID)
                    .Select(g => new { TemplateId = g.Key, Count = g.Count() })
                    .ToDictionary(x => x.TemplateId, x => x.Count);

                var exerciseCounts = (from wpd in context.WorkoutProgramDays
                                    join wpe in context.WorkoutProgramExercises on wpd.WorkoutProgramDayID equals wpe.WorkoutProgramDayID
                                    where templateIds.Contains(wpd.WorkoutProgramTemplateID)
                                    group wpe by wpd.WorkoutProgramTemplateID into g
                                    select new { TemplateId = g.Key, Count = g.Count() })
                                    .ToDictionary(x => x.TemplateId, x => x.Count);

                // ✅ DTO'ları oluştur
                var result = assignments.Select(a => new MemberWorkoutProgramListDto
                {
                    MemberWorkoutProgramID = a.MemberWorkoutProgramID,
                    MemberID = a.MemberID,
                    MemberName = a.MemberName,
                    ProgramName = a.ProgramName,
                    ExperienceLevel = a.ExperienceLevel,
                    TargetGoal = a.TargetGoal,
                    StartDate = a.StartDate,
                    EndDate = a.EndDate,
                    IsActive = a.IsActive,
                    DayCount = dayCounts.GetValueOrDefault(a.TemplateId, 0),
                    ExerciseCount = exerciseCounts.GetValueOrDefault(a.TemplateId, 0)
                }).ToList();

                return new PaginatedResult<MemberWorkoutProgramListDto>(result, parameters.PageNumber, parameters.PageSize, totalCount);
            }
        }
    }
}
