using Core.Utilities.Paging;
using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System.Collections.Generic;

namespace Business.Abstract
{
    public interface IWorkoutProgramTemplateService
    {
        /// <summary>
        /// Tüm antrenman programı şablonlarını getirir (eski metod - geriye uyumluluk için)
        /// </summary>
        IDataResult<List<WorkoutProgramTemplateListDto>> GetAll();

        /// <summary>
        /// ✅ PERFORMANS OPTİMİZE EDİLMİŞ: Sayfalama ile antrenman programı şablonlarını getirir
        /// </summary>
        IDataResult<PaginatedResult<WorkoutProgramTemplateListDto>> GetAllPaginated(WorkoutProgramPagingParameters parameters);

        /// <summary>
        /// Belirli bir antrenman programı şablonunun detayını getirir
        /// </summary>
        IDataResult<WorkoutProgramTemplateDto> GetById(int templateId);

        /// <summary>
        /// Yeni antrenman programı şablonu ekler
        /// </summary>
        IResult Add(WorkoutProgramTemplateAddDto templateAddDto);

        /// <summary>
        /// Antrenman programı şablonunu günceller
        /// </summary>
        IResult Update(WorkoutProgramTemplateUpdateDto templateUpdateDto);

        /// <summary>
        /// Antrenman programı şablonunu siler (soft delete)
        /// </summary>
        IResult Delete(int templateId);
    }
}
